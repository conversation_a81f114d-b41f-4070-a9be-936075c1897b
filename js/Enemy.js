/**
 * 敌机AI模块 - 管理敌机行为逻辑
 * 包括AI巡逻、攻击模式、敌机生成和销毁管理
 */

/**
 * 敌机类 - 单个敌机的行为
 */
export class EnemyAircraft {
    constructor(scene, weaponSystem, position = new THREE.Vector3()) {
        this.scene = scene;
        this.weaponSystem = weaponSystem;
        this.type = 'enemy';
        
        // 敌机属性
        this.position = position.clone();           // 敌机位置
        this.velocity = new THREE.Vector3();        // 速度向量
        this.rotation = new THREE.Euler();         // 旋转角度
        this.speed = 8;                             // 移动速度
        this.maxSpeed = 15;                         // 最大速度
        this.rotationSpeed = 2;                     // 旋转速度
        
        // 敌机状态
        this.health = 30;                           // 血量
        this.maxHealth = 30;                        // 最大血量
        this.isActive = true;                       // 是否激活
        this.isDead = false;                        // 是否死亡
        
        // AI行为状态
        this.aiState = 'patrol';                    // AI状态: patrol, chase, attack, retreat
        this.target = null;                         // 攻击目标
        this.lastShotTime = 0;                      // 上次射击时间
        this.shootCooldown = 1.5;                   // 射击冷却时间
        this.detectionRange = 25;                   // 检测范围
        this.attackRange = 15;                      // 攻击范围
        
        // 巡逻行为
        this.patrolCenter = position.clone();       // 巡逻中心点
        this.patrolRadius = 20;                     // 巡逻半径
        this.patrolTarget = this.generatePatrolTarget(); // 当前巡逻目标点
        this.patrolSpeed = 5;                       // 巡逻速度
        
        // 3D模型
        this.mesh = null;
        this.group = new THREE.Group();
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化敌机
     */
    init() {
        this.createEnemyModel();
        this.scene.add(this.group);
        console.log('敌机创建完成');
    }
    
    /**
     * 创建敌机3D模型
     */
    createEnemyModel() {
        // 主机身 - 红色敌机
        const fuselageGeometry = new THREE.CylinderGeometry(0.25, 0.4, 2.5, 6);
        const fuselageMaterial = new THREE.MeshPhongMaterial({ 
            color: 0xff4444,
            shininess: 80
        });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2;
        fuselage.castShadow = true;
        fuselage.receiveShadow = true;
        
        // 机翼 - 较小的机翼
        const wingGeometry = new THREE.BoxGeometry(3, 0.08, 0.8);
        const wingMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x8B0000
        });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        wings.castShadow = true;
        
        // 尾翼
        const tailGeometry = new THREE.BoxGeometry(0.4, 1.2, 0.08);
        const tailMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x8B0000
        });
        const tail = new THREE.Mesh(tailGeometry, tailMaterial);
        tail.position.set(-1.0, 0, 0);
        tail.castShadow = true;
        
        // 驾驶舱 - 深色
        const cockpitGeometry = new THREE.SphereGeometry(0.3, 6, 4);
        const cockpitMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x2F2F2F,
            transparent: true,
            opacity: 0.8
        });
        const cockpit = new THREE.Mesh(cockpitGeometry, cockpitMaterial);
        cockpit.position.set(0.6, 0.15, 0);
        cockpit.scale.set(1, 0.5, 0.7);
        
        // 推进器
        const thrusterGeometry = new THREE.ConeGeometry(0.15, 0.8, 6);
        const thrusterMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xff6600,
            transparent: true,
            opacity: 0.7
        });
        this.thrusterFlame = new THREE.Mesh(thrusterGeometry, thrusterMaterial);
        this.thrusterFlame.position.set(-1.5, 0, 0);
        this.thrusterFlame.rotation.z = -Math.PI / 2;
        
        // 组装敌机
        this.group.add(fuselage);
        this.group.add(wings);
        this.group.add(tail);
        this.group.add(cockpit);
        this.group.add(this.thrusterFlame);
        
        // 设置初始位置和旋转
        this.group.position.copy(this.position);
        
        this.mesh = this.group;
    }
    
    /**
     * 更新敌机状态
     */
    update(deltaTime, player) {
        if (!this.isActive || this.isDead) return;
        
        // 更新AI行为
        this.updateAI(deltaTime, player);
        
        // 更新移动
        this.updateMovement(deltaTime);
        
        // 更新视觉效果
        this.updateVisualEffects(deltaTime);
        
        // 边界检查
        this.checkBoundaries();
    }
    
    /**
     * 更新AI行为
     */
    updateAI(deltaTime, player) {
        if (!player || !player.isActive) {
            this.aiState = 'patrol';
            this.target = null;
            return;
        }
        
        const distanceToPlayer = this.position.distanceTo(player.position);
        
        // 状态机逻辑
        switch (this.aiState) {
            case 'patrol':
                this.patrolBehavior(deltaTime);
                
                // 检测玩家
                if (distanceToPlayer < this.detectionRange) {
                    this.aiState = 'chase';
                    this.target = player;
                    console.log('敌机发现玩家，开始追击');
                }
                break;
                
            case 'chase':
                this.chaseBehavior(deltaTime, player);
                
                // 进入攻击范围
                if (distanceToPlayer < this.attackRange) {
                    this.aiState = 'attack';
                }
                
                // 玩家逃脱
                if (distanceToPlayer > this.detectionRange * 1.5) {
                    this.aiState = 'patrol';
                    this.target = null;
                    console.log('敌机失去目标，返回巡逻');
                }
                break;
                
            case 'attack':
                this.attackBehavior(deltaTime, player);
                
                // 离开攻击范围
                if (distanceToPlayer > this.attackRange * 1.2) {
                    this.aiState = 'chase';
                }
                
                // 玩家逃脱
                if (distanceToPlayer > this.detectionRange * 1.5) {
                    this.aiState = 'patrol';
                    this.target = null;
                }
                break;
        }
    }
    
    /**
     * 巡逻行为
     */
    patrolBehavior(deltaTime) {
        const distanceToPatrolTarget = this.position.distanceTo(this.patrolTarget);
        
        // 到达巡逻点，生成新的巡逻目标
        if (distanceToPatrolTarget < 2) {
            this.patrolTarget = this.generatePatrolTarget();
        }
        
        // 朝向巡逻目标移动
        const direction = this.patrolTarget.clone().sub(this.position).normalize();
        this.velocity.copy(direction.multiplyScalar(this.patrolSpeed));
        
        // 调整朝向
        this.lookAt(this.patrolTarget);
    }
    
    /**
     * 追击行为
     */
    chaseBehavior(deltaTime, player) {
        // 朝向玩家移动
        const direction = player.position.clone().sub(this.position).normalize();
        this.velocity.copy(direction.multiplyScalar(this.speed));
        
        // 调整朝向
        this.lookAt(player.position);
    }
    
    /**
     * 攻击行为
     */
    attackBehavior(deltaTime, player) {
        // 保持距离并射击
        const direction = player.position.clone().sub(this.position).normalize();
        const distance = this.position.distanceTo(player.position);
        
        // 如果太近就后退，太远就前进
        if (distance < this.attackRange * 0.7) {
            this.velocity.copy(direction.multiplyScalar(-this.speed * 0.5)); // 后退
        } else if (distance > this.attackRange * 0.9) {
            this.velocity.copy(direction.multiplyScalar(this.speed * 0.3)); // 前进
        } else {
            // 侧向移动
            const sideDirection = new THREE.Vector3(-direction.z, 0, direction.x);
            this.velocity.copy(sideDirection.multiplyScalar(this.speed * 0.4));
        }
        
        // 始终朝向玩家
        this.lookAt(player.position);
        
        // 尝试射击
        this.tryShoot(player);
    }
    
    /**
     * 尝试射击
     */
    tryShoot(target) {
        const currentTime = Date.now() / 1000;
        
        if (currentTime - this.lastShotTime > this.shootCooldown) {
            const shootPosition = this.getShootPosition();
            const shootDirection = this.getShootDirection(target);
            
            if (this.weaponSystem) {
                const success = this.weaponSystem.fire(shootPosition, shootDirection, 'enemy');
                if (success) {
                    this.lastShotTime = currentTime;
                    console.log('敌机射击!');
                }
            }
        }
    }
    
    /**
     * 生成巡逻目标点
     */
    generatePatrolTarget() {
        const angle = Math.random() * Math.PI * 2;
        const radius = Math.random() * this.patrolRadius;
        
        return new THREE.Vector3(
            this.patrolCenter.x + Math.cos(angle) * radius,
            this.patrolCenter.y + (Math.random() - 0.5) * 10,
            this.patrolCenter.z + Math.sin(angle) * radius
        );
    }
    
    /**
     * 朝向目标
     */
    lookAt(target) {
        const direction = target.clone().sub(this.position).normalize();
        const targetRotation = Math.atan2(direction.z, direction.x);
        
        // 平滑旋转
        let currentRotation = this.group.rotation.y;
        let rotationDiff = targetRotation - currentRotation;
        
        // 处理角度环绕
        if (rotationDiff > Math.PI) rotationDiff -= Math.PI * 2;
        if (rotationDiff < -Math.PI) rotationDiff += Math.PI * 2;
        
        this.group.rotation.y += rotationDiff * 0.05;
    }
    
    /**
     * 更新移动
     */
    updateMovement(deltaTime) {
        // 限制速度
        if (this.velocity.length() > this.maxSpeed) {
            this.velocity.normalize().multiplyScalar(this.maxSpeed);
        }
        
        // 更新位置
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
        this.group.position.copy(this.position);
        
        // 应用阻力
        this.velocity.multiplyScalar(0.98);
    }
    
    /**
     * 更新视觉效果
     */
    updateVisualEffects(deltaTime) {
        // 推进器火焰效果
        if (this.velocity.length() > 1) {
            this.thrusterFlame.visible = true;
            const time = Date.now() * 0.008;
            this.thrusterFlame.scale.y = 0.8 + Math.sin(time) * 0.2;
        } else {
            this.thrusterFlame.visible = false;
        }
    }
    
    /**
     * 边界检查
     */
    checkBoundaries() {
        const boundary = 60;
        
        // 如果超出边界，朝向中心移动
        if (Math.abs(this.position.x) > boundary || 
            Math.abs(this.position.y) > boundary || 
            Math.abs(this.position.z) > boundary) {
            
            const centerDirection = new THREE.Vector3(0, 0, 0).sub(this.position).normalize();
            this.velocity.add(centerDirection.multiplyScalar(2));
        }
    }
    
    /**
     * 获取射击位置
     */
    getShootPosition() {
        const shootOffset = new THREE.Vector3(1.2, 0, 0);
        shootOffset.applyEuler(this.group.rotation);
        return this.position.clone().add(shootOffset);
    }
    
    /**
     * 获取射击方向（预测玩家位置）
     */
    getShootDirection(target) {
        // 简单的预测射击
        const targetVelocity = target.velocity || new THREE.Vector3();
        const timeToTarget = this.position.distanceTo(target.position) / 50; // 假设炮弹速度50
        const predictedPosition = target.position.clone().add(targetVelocity.clone().multiplyScalar(timeToTarget));
        
        return predictedPosition.sub(this.position).normalize();
    }
    
    /**
     * 受到伤害
     */
    takeDamage(damage) {
        if (!this.isActive || this.isDead) return false;
        
        this.health -= damage;
        this.health = Math.max(0, this.health);
        
        console.log(`敌机受到 ${damage} 点伤害，剩余血量: ${this.health}`);
        
        // 受伤时切换到攻击状态
        if (this.health > 0 && this.aiState === 'patrol') {
            this.aiState = 'chase';
        }
        
        // 检查是否死亡
        if (this.health <= 0) {
            this.destroy();
            return true;
        }
        
        return false;
    }
    
    /**
     * 销毁敌机
     */
    destroy() {
        this.isDead = true;
        this.isActive = false;
        console.log('敌机被摧毁');
        
        // 播放爆炸效果（后续实现）
        // this.playExplosionEffect();
        
        // 延迟移除以显示爆炸效果
        setTimeout(() => {
            this.dispose();
        }, 100);
    }
    
    /**
     * 清理资源
     */
    dispose() {
        if (this.group && this.scene) {
            this.scene.remove(this.group);
        }
        console.log('敌机资源已清理');
    }
}

/**
 * 敌机管理器 - 管理所有敌机的生成和行为
 */
export class EnemyManager {
    constructor(scene, weaponSystem) {
        this.scene = scene;
        this.weaponSystem = weaponSystem;
        this.enemies = [];
        this.maxEnemies = 5;                    // 最大敌机数量
        this.spawnInterval = 3.0;               // 生成间隔（秒）
        this.lastSpawnTime = 0;                 // 上次生成时间
        this.spawnDistance = 40;                // 生成距离
    }
    
    /**
     * 更新敌机管理器
     */
    update(deltaTime, player) {
        // 更新所有敌机
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            enemy.update(deltaTime, player);
            
            // 移除死亡的敌机
            if (!enemy.isActive) {
                this.enemies.splice(i, 1);
            }
        }
        
        // 生成新敌机
        this.spawnEnemies(player);
    }
    
    /**
     * 生成敌机
     */
    spawnEnemies(player) {
        const currentTime = Date.now() / 1000;
        
        if (this.enemies.length < this.maxEnemies && 
            currentTime - this.lastSpawnTime > this.spawnInterval) {
            
            const spawnPosition = this.generateSpawnPosition(player);
            const enemy = new EnemyAircraft(this.scene, this.weaponSystem, spawnPosition);
            
            this.enemies.push(enemy);
            this.lastSpawnTime = currentTime;
            
            console.log(`生成新敌机，当前敌机数量: ${this.enemies.length}`);
        }
    }
    
    /**
     * 生成敌机位置
     */
    generateSpawnPosition(player) {
        const angle = Math.random() * Math.PI * 2;
        const distance = this.spawnDistance + Math.random() * 20;
        
        const basePosition = player ? player.position : new THREE.Vector3(0, 0, 0);
        
        return new THREE.Vector3(
            basePosition.x + Math.cos(angle) * distance,
            basePosition.y + (Math.random() - 0.5) * 20,
            basePosition.z + Math.sin(angle) * distance
        );
    }
    
    /**
     * 获取所有敌机
     */
    getEnemies() {
        return this.enemies;
    }
    
    /**
     * 清理所有敌机
     */
    clearAll() {
        this.enemies.forEach(enemy => enemy.dispose());
        this.enemies = [];
    }
}
