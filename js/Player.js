/**
 * 玩家飞机类 - 管理玩家飞机的所有行为
 * 包括移动、射击、机尾管理等功能
 */

export class PlayerAircraft {
    constructor(scene, weaponSystem = null) {
        this.scene = scene;
        this.weaponSystem = weaponSystem;
        
        // 飞机属性
        this.position = new THREE.Vector3(0, 0, 0);    // 飞机当前位置
        this.velocity = new THREE.Vector3(0, 0, 0);    // 飞机速度向量
        this.rotation = new THREE.Euler(0, 0, 0);     // 飞机旋转角度
        this.speed = 15;                               // 飞机移动速度
        this.maxSpeed = 25;                            // 最大速度
        this.acceleration = 30;                        // 加速度
        this.rotationSpeed = 3;                        // 旋转速度
        
        // 飞机状态
        this.health = 100;                             // 血量
        this.maxHealth = 100;                          // 最大血量
        this.isActive = true;                          // 是否激活
        this.invulnerable = false;                     // 是否无敌（受伤后短暂无敌）
        this.invulnerableTime = 0;                     // 无敌时间计数
        
        // 机尾节点系统
        this.tailNodes = [];                           // 机尾节点数组
        this.tailLength = 0;                           // 机尾长度
        this.nodeSpacing = 2;                          // 节点间距
        this.tailHistory = [];                         // 位置历史记录
        this.historyMaxLength = 100;                   // 历史记录最大长度
        
        // 输入控制
        this.inputState = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            up: false,
            down: false,
            shoot: false
        };
        
        // 3D模型
        this.mesh = null;                              // 飞机网格
        this.group = new THREE.Group();                // 飞机组（包含所有部件）
        
        // 初始化
        this.init();
        this.bindControls();
    }
    
    /**
     * 初始化玩家飞机
     */
    init() {
        this.createAircraftModel();
        this.scene.add(this.group);
        console.log('玩家飞机初始化完成');
    }
    
    /**
     * 创建飞机3D模型
     */
    createAircraftModel() {
        // 主机身
        const fuselageGeometry = new THREE.CylinderGeometry(0.3, 0.5, 3, 8);
        const fuselageMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x4169E1,
            shininess: 100
        });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2; // 让飞机朝向正确方向
        fuselage.castShadow = true;
        fuselage.receiveShadow = true;
        
        // 机翼
        const wingGeometry = new THREE.BoxGeometry(4, 0.1, 1);
        const wingMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x2E8B57
        });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        wings.position.set(0, 0, 0);
        wings.castShadow = true;
        
        // 尾翼
        const tailGeometry = new THREE.BoxGeometry(0.5, 1.5, 0.1);
        const tailMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x2E8B57
        });
        const tail = new THREE.Mesh(tailGeometry, tailMaterial);
        tail.position.set(-1.2, 0, 0);
        tail.castShadow = true;
        
        // 驾驶舱
        const cockpitGeometry = new THREE.SphereGeometry(0.4, 8, 6);
        const cockpitMaterial = new THREE.MeshPhongMaterial({ 
            color: 0x87CEEB,
            transparent: true,
            opacity: 0.7
        });
        const cockpit = new THREE.Mesh(cockpitGeometry, cockpitMaterial);
        cockpit.position.set(0.8, 0.2, 0);
        cockpit.scale.set(1, 0.6, 0.8);
        
        // 推进器火焰效果（初始隐藏）
        const thrusterGeometry = new THREE.ConeGeometry(0.2, 1, 6);
        const thrusterMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xff4500,
            transparent: true,
            opacity: 0.8
        });
        this.thrusterFlame = new THREE.Mesh(thrusterGeometry, thrusterMaterial);
        this.thrusterFlame.position.set(-1.8, 0, 0);
        this.thrusterFlame.rotation.z = -Math.PI / 2;
        this.thrusterFlame.visible = false;
        
        // 组装飞机
        this.group.add(fuselage);
        this.group.add(wings);
        this.group.add(tail);
        this.group.add(cockpit);
        this.group.add(this.thrusterFlame);
        
        // 设置初始位置
        this.group.position.copy(this.position);
        
        this.mesh = this.group; // 保存引用
    }
    
    /**
     * 绑定控制事件
     */
    bindControls() {
        // 键盘事件监听
        document.addEventListener('keydown', (event) => {
            this.onKeyDown(event);
        });
        
        document.addEventListener('keyup', (event) => {
            this.onKeyUp(event);
        });
        
        // 鼠标事件监听
        document.addEventListener('mousedown', (event) => {
            if (event.button === 0) { // 左键
                this.inputState.shoot = true;
            }
        });
        
        document.addEventListener('mouseup', (event) => {
            if (event.button === 0) { // 左键
                this.inputState.shoot = false;
            }
        });
    }
    
    /**
     * 键盘按下事件处理
     */
    onKeyDown(event) {
        switch (event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.inputState.forward = true;
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.inputState.backward = true;
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.inputState.left = true;
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.inputState.right = true;
                break;
            case 'KeyQ':
                this.inputState.up = true;
                break;
            case 'KeyE':
                this.inputState.down = true;
                break;
            case 'Space':
                this.inputState.shoot = true;
                event.preventDefault(); // 防止页面滚动
                break;
        }
    }
    
    /**
     * 键盘释放事件处理
     */
    onKeyUp(event) {
        switch (event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.inputState.forward = false;
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.inputState.backward = false;
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.inputState.left = false;
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.inputState.right = false;
                break;
            case 'KeyQ':
                this.inputState.up = false;
                break;
            case 'KeyE':
                this.inputState.down = false;
                break;
            case 'Space':
                this.inputState.shoot = false;
                break;
        }
    }
    
    /**
     * 更新飞机状态
     * @param {number} deltaTime - 时间间隔
     */
    update(deltaTime) {
        if (!this.isActive) return;
        
        // 更新输入处理
        this.updateMovement(deltaTime);
        
        // 更新位置历史
        this.updatePositionHistory();
        
        // 更新机尾节点
        this.updateTailNodes(deltaTime);
        
        // 处理射击输入
        this.handleShooting();

        // 更新视觉效果
        this.updateVisualEffects(deltaTime);

        // 更新无敌状态
        this.updateInvulnerability(deltaTime);

        // 边界检查
        this.checkBoundaries();
    }
    
    /**
     * 更新移动逻辑
     */
    updateMovement(deltaTime) {
        const moveVector = new THREE.Vector3();
        const rotationVector = new THREE.Vector3();
        
        // 计算移动方向
        if (this.inputState.forward) {
            moveVector.z -= 1;
        }
        if (this.inputState.backward) {
            moveVector.z += 1;
        }
        if (this.inputState.left) {
            moveVector.x -= 1;
            rotationVector.z += 1; // 向左倾斜
        }
        if (this.inputState.right) {
            moveVector.x += 1;
            rotationVector.z -= 1; // 向右倾斜
        }
        if (this.inputState.up) {
            moveVector.y += 1;
            rotationVector.x -= 1; // 向上俯仰
        }
        if (this.inputState.down) {
            moveVector.y -= 1;
            rotationVector.x += 1; // 向下俯仰
        }
        
        // 标准化移动向量
        if (moveVector.length() > 0) {
            moveVector.normalize();
            
            // 应用加速度
            this.velocity.add(moveVector.multiplyScalar(this.acceleration * deltaTime));
            
            // 限制最大速度
            if (this.velocity.length() > this.maxSpeed) {
                this.velocity.normalize().multiplyScalar(this.maxSpeed);
            }
            
            // 显示推进器火焰
            this.thrusterFlame.visible = true;
        } else {
            // 应用阻力
            this.velocity.multiplyScalar(0.95);
            
            // 隐藏推进器火焰
            this.thrusterFlame.visible = false;
        }
        
        // 更新位置
        this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
        this.group.position.copy(this.position);
        
        // 更新旋转（飞行姿态）
        const targetRotation = new THREE.Euler(
            rotationVector.x * 0.3,  // 俯仰角度
            0,                       // 偏航角度
            rotationVector.z * 0.5   // 翻滚角度
        );
        
        // 平滑过渡到目标旋转
        this.group.rotation.x = THREE.MathUtils.lerp(this.group.rotation.x, targetRotation.x, deltaTime * 5);
        this.group.rotation.z = THREE.MathUtils.lerp(this.group.rotation.z, targetRotation.z, deltaTime * 5);
    }
    
    /**
     * 更新位置历史记录
     */
    updatePositionHistory() {
        // 添加当前位置到历史记录
        this.tailHistory.push(this.position.clone());
        
        // 限制历史记录长度
        if (this.tailHistory.length > this.historyMaxLength) {
            this.tailHistory.shift();
        }
    }
    
    /**
     * 处理射击输入
     */
    handleShooting() {
        if (this.inputState.shoot && this.weaponSystem) {
            const shootPosition = this.getShootPosition();
            const shootDirection = this.getShootDirection();
            this.weaponSystem.fire(shootPosition, shootDirection, 'player');
        }
    }

    /**
     * 更新机尾节点
     */
    updateTailNodes(deltaTime) {
        // 机尾节点逻辑将在贪吃蛇机制阶段实现
        // 这里先预留接口
    }
    
    /**
     * 更新视觉效果
     */
    updateVisualEffects(deltaTime) {
        // 推进器火焰动画
        if (this.thrusterFlame.visible) {
            const time = Date.now() * 0.01;
            this.thrusterFlame.scale.y = 1 + Math.sin(time) * 0.3;
            this.thrusterFlame.material.opacity = 0.6 + Math.sin(time * 2) * 0.2;
        }
        
        // 无敌状态闪烁效果
        if (this.invulnerable) {
            const blinkSpeed = 10;
            this.group.visible = Math.sin(Date.now() * blinkSpeed * 0.01) > 0;
        } else {
            this.group.visible = true;
        }
    }
    
    /**
     * 更新无敌状态
     */
    updateInvulnerability(deltaTime) {
        if (this.invulnerable) {
            this.invulnerableTime -= deltaTime;
            if (this.invulnerableTime <= 0) {
                this.invulnerable = false;
                this.group.visible = true;
            }
        }
    }
    
    /**
     * 边界检查
     */
    checkBoundaries() {
        const boundary = 50; // 边界范围
        
        // X轴边界
        if (this.position.x > boundary) {
            this.position.x = boundary;
            this.velocity.x = Math.min(0, this.velocity.x);
        } else if (this.position.x < -boundary) {
            this.position.x = -boundary;
            this.velocity.x = Math.max(0, this.velocity.x);
        }
        
        // Y轴边界
        if (this.position.y > boundary) {
            this.position.y = boundary;
            this.velocity.y = Math.min(0, this.velocity.y);
        } else if (this.position.y < -boundary) {
            this.position.y = -boundary;
            this.velocity.y = Math.max(0, this.velocity.y);
        }
        
        // Z轴边界
        if (this.position.z > boundary) {
            this.position.z = boundary;
            this.velocity.z = Math.min(0, this.velocity.z);
        } else if (this.position.z < -boundary) {
            this.position.z = -boundary;
            this.velocity.z = Math.max(0, this.velocity.z);
        }
        
        // 更新位置
        this.group.position.copy(this.position);
    }
    
    /**
     * 受到伤害
     */
    takeDamage(damage) {
        if (this.invulnerable || !this.isActive) return false;
        
        this.health -= damage;
        this.health = Math.max(0, this.health);
        
        // 设置短暂无敌
        this.invulnerable = true;
        this.invulnerableTime = 1.0; // 1秒无敌时间
        
        console.log(`玩家受到 ${damage} 点伤害，剩余血量: ${this.health}`);
        
        // 检查是否死亡
        if (this.health <= 0) {
            this.destroy();
            return true; // 返回true表示死亡
        }
        
        return false;
    }
    
    /**
     * 治疗
     */
    heal(amount) {
        this.health += amount;
        this.health = Math.min(this.maxHealth, this.health);
        console.log(`玩家恢复 ${amount} 点血量，当前血量: ${this.health}`);
    }
    
    /**
     * 获取射击位置
     */
    getShootPosition() {
        const shootOffset = new THREE.Vector3(1.5, 0, 0); // 从机头发射
        return this.position.clone().add(shootOffset);
    }
    
    /**
     * 获取射击方向
     */
    getShootDirection() {
        const direction = new THREE.Vector3(1, 0, 0); // 向前射击
        direction.applyEuler(this.group.rotation);
        return direction.normalize();
    }
    
    /**
     * 销毁飞机
     */
    destroy() {
        this.isActive = false;
        console.log('玩家飞机被摧毁');
        
        // 播放爆炸效果（后续实现）
        // this.playExplosionEffect();
        
        // 触发游戏结束
        // gameEngine.gameOver();
    }
    
    /**
     * 清理资源
     */
    dispose() {
        if (this.group && this.scene) {
            this.scene.remove(this.group);
        }
        
        // 清理机尾节点
        this.tailNodes.forEach(node => {
            if (node.mesh && this.scene) {
                this.scene.remove(node.mesh);
            }
        });
        
        this.tailNodes = [];
        this.tailHistory = [];
        
        console.log('玩家飞机资源已清理');
    }
}
