/**
 * 游戏引擎模块 - 管理主游戏循环和渲染
 * 负责场景初始化、资源加载、全局游戏状态管理
 */

export class GameEngine {
    constructor() {
        // 渲染相关
        this.scene = null;           // Three.js场景
        this.camera = null;          // 相机
        this.renderer = null;        // 渲染器
        this.canvas = null;          // 画布元素
        
        // 游戏状态
        this.isRunning = false;      // 游戏是否运行中
        this.isPaused = false;       // 游戏是否暂停
        this.gameState = 'loading';  // 游戏状态: loading, playing, paused, gameOver
        
        // 时间管理
        this.clock = new THREE.Clock();
        this.deltaTime = 0;
        this.lastTime = 0;
        
        // 游戏对象管理
        this.gameObjects = [];       // 所有游戏对象
        this.player = null;          // 玩家对象
        this.enemyManager = null;    // 敌机管理器
        this.weaponSystem = null;    // 武器系统
        
        // 性能监控
        this.frameCount = 0;
        this.fps = 0;
        this.lastFpsUpdate = 0;
        
        // 事件绑定
        this.boundUpdate = this.update.bind(this);
        this.boundResize = this.onWindowResize.bind(this);
    }
    
    /**
     * 初始化游戏引擎
     * 设置渲染器、相机、场景和基础环境
     */
    async init() {
        try {
            console.log('正在初始化游戏引擎...');
            
            // 获取画布元素
            this.canvas = document.getElementById('gameCanvas');
            if (!this.canvas) {
                throw new Error('找不到游戏画布元素');
            }
            
            // 初始化渲染器
            this.initRenderer();
            
            // 初始化相机
            this.initCamera();
            
            // 初始化场景
            this.initScene();
            
            // 设置光照
            this.setupLighting();
            
            // 设置环境
            this.setupEnvironment();
            
            // 绑定事件
            this.bindEvents();
            
            console.log('游戏引擎初始化完成');
            return true;
            
        } catch (error) {
            console.error('游戏引擎初始化失败:', error);
            return false;
        }
    }
    
    /**
     * 初始化WebGL渲染器
     */
    initRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true,        // 抗锯齿
            alpha: false,           // 不需要透明背景
            powerPreference: "high-performance"  // 高性能模式
        });
        
        // 设置渲染器参数
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // 限制像素比以提高性能
        this.renderer.setClearColor(0x000011, 1.0); // 深蓝色背景
        
        // 启用阴影
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        console.log('渲染器初始化完成');
    }
    
    /**
     * 初始化相机
     */
    initCamera() {
        const aspect = window.innerWidth / window.innerHeight;
        this.camera = new THREE.PerspectiveCamera(
            75,      // 视野角度
            aspect,  // 宽高比
            0.1,     // 近裁剪面
            1000     // 远裁剪面
        );
        
        // 设置相机初始位置（跟随玩家飞机后方）
        this.camera.position.set(0, 5, 10);
        this.camera.lookAt(0, 0, 0);
        
        console.log('相机初始化完成');
    }
    
    /**
     * 初始化场景
     */
    initScene() {
        this.scene = new THREE.Scene();
        
        // 设置雾效果增加深度感
        this.scene.fog = new THREE.Fog(0x000011, 50, 200);
        
        console.log('场景初始化完成');
    }
    
    /**
     * 设置光照系统
     */
    setupLighting() {
        // 环境光 - 提供基础照明
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        
        // 主方向光 - 模拟太阳光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        
        // 设置阴影参数
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 100;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        
        this.scene.add(directionalLight);
        
        // 补充光源 - 从另一个角度照亮场景
        const fillLight = new THREE.DirectionalLight(0x4080ff, 0.3);
        fillLight.position.set(-5, 5, -5);
        this.scene.add(fillLight);
        
        console.log('光照系统设置完成');
    }
    
    /**
     * 设置环境（天空盒、地面等）
     */
    setupEnvironment() {
        // 创建星空背景
        this.createStarField();
        
        // 创建地面参考平面（可选）
        this.createGroundPlane();
        
        console.log('环境设置完成');
    }
    
    /**
     * 创建星空背景
     */
    createStarField() {
        const starGeometry = new THREE.BufferGeometry();
        const starCount = 1000;
        const positions = new Float32Array(starCount * 3);
        
        // 随机生成星星位置
        for (let i = 0; i < starCount * 3; i += 3) {
            positions[i] = (Math.random() - 0.5) * 400;     // x
            positions[i + 1] = (Math.random() - 0.5) * 400; // y
            positions[i + 2] = (Math.random() - 0.5) * 400; // z
        }
        
        starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const starMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 2,
            sizeAttenuation: false
        });
        
        const stars = new THREE.Points(starGeometry, starMaterial);
        this.scene.add(stars);
    }
    
    /**
     * 创建地面参考平面
     */
    createGroundPlane() {
        const groundGeometry = new THREE.PlaneGeometry(200, 200, 20, 20);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x003300,
            transparent: true,
            opacity: 0.3,
            wireframe: true
        });
        
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -20;
        ground.receiveShadow = true;
        
        this.scene.add(ground);
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        window.addEventListener('resize', this.boundResize);
        
        // 键盘事件
        document.addEventListener('keydown', (event) => {
            this.onKeyDown(event);
        });
        
        // 鼠标事件
        this.canvas.addEventListener('click', (event) => {
            this.onMouseClick(event);
        });
    }
    
    /**
     * 窗口大小改变处理
     */
    onWindowResize() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        
        this.renderer.setSize(width, height);
    }
    
    /**
     * 键盘按下事件处理
     */
    onKeyDown(event) {
        switch (event.code) {
            case 'KeyP':
                this.togglePause();
                break;
            case 'KeyR':
                this.restart();
                break;
        }
    }
    
    /**
     * 鼠标点击事件处理
     */
    onMouseClick(event) {
        // 触发射击
        if (this.player && this.weaponSystem && this.gameState === 'playing') {
            this.handleShooting();
        }
    }

    /**
     * 处理射击逻辑
     */
    handleShooting() {
        if (!this.player || !this.weaponSystem) return;

        const shootPosition = this.player.getShootPosition();
        const shootDirection = this.player.getShootDirection();

        const success = this.weaponSystem.fire(shootPosition, shootDirection, 'player');
        if (success) {
            console.log('玩家射击!');
        }
    }
    
    /**
     * 开始游戏
     */
    start() {
        this.isRunning = true;
        this.gameState = 'playing';
        this.clock.start();
        
        // 隐藏加载界面
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'none';
        }
        
        // 开始渲染循环
        this.update();
        
        console.log('游戏开始');
    }
    
    /**
     * 暂停/恢复游戏
     */
    togglePause() {
        this.isPaused = !this.isPaused;
        this.gameState = this.isPaused ? 'paused' : 'playing';
        
        const statusElement = document.getElementById('gameStatus');
        if (statusElement) {
            statusElement.style.display = this.isPaused ? 'block' : 'none';
        }
        
        console.log(this.isPaused ? '游戏暂停' : '游戏恢复');
    }
    
    /**
     * 重新开始游戏
     */
    restart() {
        console.log('重新开始游戏');
        // 重置游戏状态
        this.gameState = 'playing';
        this.isPaused = false;
        
        // 清理游戏对象
        this.clearGameObjects();
        
        // 重新初始化游戏对象
        // 这里将在后续模块中实现
    }
    
    /**
     * 主更新循环
     */
    update() {
        if (!this.isRunning) return;
        
        // 计算时间差
        this.deltaTime = this.clock.getDelta();
        
        // 更新FPS计数
        this.updateFPS();
        
        // 如果游戏未暂停，更新游戏逻辑
        if (!this.isPaused && this.gameState === 'playing') {
            this.updateGameLogic();
        }
        
        // 渲染场景
        this.render();
        
        // 请求下一帧
        requestAnimationFrame(this.boundUpdate);
    }
    
    /**
     * 更新游戏逻辑
     */
    updateGameLogic() {
        // 更新所有游戏对象
        this.gameObjects.forEach(obj => {
            if (obj.update) {
                obj.update(this.deltaTime);
            }
        });
        
        // 更新玩家
        if (this.player && this.player.update) {
            this.player.update(this.deltaTime);
        }
        
        // 更新敌机管理器
        if (this.enemyManager) {
            this.enemyManager.update(this.deltaTime, this.player);
        }

        // 更新武器系统
        if (this.weaponSystem) {
            this.weaponSystem.update(this.deltaTime);
        }
        
        // 碰撞检测
        this.checkCollisions();
        
        // 清理无效对象
        this.cleanupObjects();
    }
    
    /**
     * 碰撞检测
     */
    checkCollisions() {
        if (!this.weaponSystem || !this.player || !this.enemyManager) return;

        // 准备碰撞检测目标
        const targets = [];

        // 添加玩家到目标列表（供敌机炮弹检测）
        if (this.player.isActive) {
            targets.push({
                ...this.player,
                type: 'player'
            });
        }

        // 添加敌机到目标列表（供玩家炮弹检测）
        const enemies = this.enemyManager.getEnemies();
        enemies.forEach(enemy => {
            if (enemy.isActive) {
                targets.push({
                    ...enemy,
                    type: 'enemy'
                });
            }
        });

        // 检测炮弹碰撞
        const hits = this.weaponSystem.checkCollisions(targets);

        // 处理碰撞结果
        hits.forEach(hit => {
            const { target, damage } = hit;

            if (target.type === 'player') {
                const died = this.player.takeDamage(damage);
                if (died) {
                    this.gameOver();
                }
            } else if (target.type === 'enemy') {
                const died = target.takeDamage(damage);
                if (died) {
                    this.onEnemyDestroyed(target);
                }
            }
        });
    }

    /**
     * 敌机被摧毁时的处理
     */
    onEnemyDestroyed(enemy) {
        console.log('敌机被摧毁，玩家得分!');

        // 增加机尾长度
        if (this.player && this.player.addTailNode) {
            this.player.addTailNode();
        }

        // 增加分数（后续实现）
        // this.score += 100;

        // 播放摧毁音效（后续实现）
        // this.audioManager.playSound('enemyDestroyed');
    }

    /**
     * 游戏结束
     */
    gameOver() {
        this.gameState = 'gameOver';
        this.isPaused = true;

        const statusElement = document.getElementById('gameStatus');
        const statusText = document.getElementById('statusText');

        if (statusElement && statusText) {
            statusText.textContent = '游戏结束';
            statusElement.style.display = 'block';
        }

        console.log('游戏结束');
    }
    
    /**
     * 清理无效对象
     */
    cleanupObjects() {
        // 清理超出边界或已销毁的对象
        // 敌机清理由敌机管理器自动处理
        // 炮弹清理由武器系统自动处理
    }
    
    /**
     * 渲染场景
     */
    render() {
        this.renderer.render(this.scene, this.camera);
    }
    
    /**
     * 更新FPS计数
     */
    updateFPS() {
        this.frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - this.lastFpsUpdate >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFpsUpdate = currentTime;
        }
    }
    
    /**
     * 清理游戏对象
     */
    clearGameObjects() {
        this.gameObjects.forEach(obj => {
            if (obj.dispose) {
                obj.dispose();
            }
        });

        this.gameObjects = [];

        // 清理敌机管理器
        if (this.enemyManager) {
            this.enemyManager.clearAll();
        }

        // 清理武器系统
        if (this.weaponSystem) {
            this.weaponSystem.clearAllBullets();
        }
    }
    
    /**
     * 销毁游戏引擎
     */
    dispose() {
        this.isRunning = false;
        
        // 移除事件监听器
        window.removeEventListener('resize', this.boundResize);
        
        // 清理游戏对象
        this.clearGameObjects();
        
        // 清理Three.js资源
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        console.log('游戏引擎已销毁');
    }
}
