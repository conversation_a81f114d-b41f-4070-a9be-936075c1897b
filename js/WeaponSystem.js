/**
 * 武器系统模块 - 管理炮弹发射、轨迹和碰撞检测
 * 包括不同武器类型、弹药管理和射击特效
 */

/**
 * 炮弹类 - 单个炮弹的行为
 */
export class Bullet {
    constructor(position, direction, speed = 50, damage = 10, owner = 'player') {
        this.position = position.clone();       // 炮弹位置
        this.direction = direction.clone();     // 飞行方向
        this.speed = speed;                     // 飞行速度
        this.damage = damage;                   // 伤害值
        this.owner = owner;                     // 发射者（'player' 或 'enemy'）
        this.isActive = true;                   // 是否激活
        this.lifeTime = 5.0;                    // 生存时间（秒）
        this.age = 0;                           // 已存在时间
        
        // 3D模型
        this.mesh = null;
        this.trail = [];                        // 轨迹粒子
        this.maxTrailLength = 10;               // 最大轨迹长度
        
        this.createBulletModel();
    }
    
    /**
     * 创建炮弹3D模型
     */
    createBulletModel() {
        // 炮弹几何体
        const geometry = new THREE.SphereGeometry(0.1, 8, 6);
        
        // 根据发射者设置不同颜色
        const color = this.owner === 'player' ? 0x00ffff : 0xff4444;
        const material = new THREE.MeshBasicMaterial({ 
            color: color,
            emissive: color,
            emissiveIntensity: 0.3
        });
        
        this.mesh = new THREE.Mesh(geometry, material);
        this.mesh.position.copy(this.position);
        
        // 添加发光效果
        const glowGeometry = new THREE.SphereGeometry(0.2, 8, 6);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.3
        });
        const glow = new THREE.Mesh(glowGeometry, glowMaterial);
        this.mesh.add(glow);
    }
    
    /**
     * 更新炮弹状态
     */
    update(deltaTime) {
        if (!this.isActive) return;
        
        // 更新年龄
        this.age += deltaTime;
        
        // 检查生存时间
        if (this.age >= this.lifeTime) {
            this.destroy();
            return;
        }
        
        // 更新位置
        const movement = this.direction.clone().multiplyScalar(this.speed * deltaTime);
        this.position.add(movement);
        this.mesh.position.copy(this.position);
        
        // 更新轨迹
        this.updateTrail();
        
        // 更新视觉效果
        this.updateVisualEffects();
    }
    
    /**
     * 更新轨迹效果
     */
    updateTrail() {
        // 添加当前位置到轨迹
        this.trail.push(this.position.clone());
        
        // 限制轨迹长度
        if (this.trail.length > this.maxTrailLength) {
            this.trail.shift();
        }
    }
    
    /**
     * 更新视觉效果
     */
    updateVisualEffects() {
        // 炮弹发光效果
        const intensity = 0.3 + Math.sin(Date.now() * 0.01) * 0.1;
        this.mesh.material.emissiveIntensity = intensity;
        
        // 根据年龄调整透明度
        const alpha = 1.0 - (this.age / this.lifeTime) * 0.5;
        this.mesh.material.opacity = alpha;
    }
    
    /**
     * 检查与目标的碰撞
     */
    checkCollision(target) {
        if (!this.isActive || !target.mesh) return false;
        
        const distance = this.position.distanceTo(target.position);
        const collisionDistance = 1.0; // 碰撞检测距离
        
        return distance < collisionDistance;
    }
    
    /**
     * 命中目标
     */
    hit(target) {
        console.log(`炮弹命中目标，造成 ${this.damage} 点伤害`);
        
        // 创建命中特效
        this.createHitEffect();
        
        // 销毁炮弹
        this.destroy();
        
        return this.damage;
    }
    
    /**
     * 创建命中特效
     */
    createHitEffect() {
        // 简单的爆炸效果（后续可以用粒子系统增强）
        const explosionGeometry = new THREE.SphereGeometry(0.5, 8, 6);
        const explosionMaterial = new THREE.MeshBasicMaterial({
            color: 0xffaa00,
            transparent: true,
            opacity: 0.8
        });
        
        const explosion = new THREE.Mesh(explosionGeometry, explosionMaterial);
        explosion.position.copy(this.position);
        
        // 添加到场景（需要从外部传入scene引用）
        // 这里先预留，后续在武器系统中实现
    }
    
    /**
     * 重置炮弹（用于对象池）
     */
    reset(position, direction, speed, damage, owner) {
        this.position.copy(position);
        this.direction.copy(direction);
        this.speed = speed;
        this.damage = damage;
        this.owner = owner;
        this.isActive = true;
        this.age = 0;

        // 重置网格位置
        if (this.mesh) {
            this.mesh.position.copy(this.position);
        }

        // 清空轨迹
        this.trail = [];
    }

    /**
     * 销毁炮弹
     */
    destroy() {
        this.isActive = false;
        console.log('炮弹被销毁');
    }
    
    /**
     * 清理资源
     */
    dispose(scene) {
        if (this.mesh && scene) {
            scene.remove(this.mesh);
        }
    }
}

/**
 * 武器系统类 - 管理所有武器和炮弹
 */
export class WeaponSystem {
    constructor(scene) {
        this.scene = scene;
        this.bullets = [];                      // 所有炮弹数组
        this.bulletPool = [];                   // 炮弹对象池
        this.maxBullets = 100;                  // 最大炮弹数量
        
        // 武器配置
        this.weapons = {
            basic: {
                damage: 10,
                speed: 50,
                fireRate: 0.2,              // 射击间隔（秒）
                bulletCount: 1              // 每次发射的炮弹数
            },
            rapid: {
                damage: 5,
                speed: 60,
                fireRate: 0.1,
                bulletCount: 1
            },
            spread: {
                damage: 8,
                speed: 45,
                fireRate: 0.3,
                bulletCount: 3              // 散射
            }
        };
        
        this.currentWeapon = 'basic';           // 当前武器类型
        this.lastFireTime = 0;                  // 上次射击时间
    }
    
    /**
     * 发射炮弹
     */
    fire(position, direction, owner = 'player', weaponType = null) {
        const weapon = this.weapons[weaponType || this.currentWeapon];
        const currentTime = Date.now() / 1000;
        
        // 检查射击冷却
        if (currentTime - this.lastFireTime < weapon.fireRate) {
            return false; // 冷却中，无法射击
        }
        
        // 检查炮弹数量限制
        if (this.bullets.length >= this.maxBullets) {
            return false; // 炮弹数量已达上限
        }
        
        // 发射炮弹
        for (let i = 0; i < weapon.bulletCount; i++) {
            let bulletDirection = direction.clone();
            
            // 如果是散射武器，调整方向
            if (weapon.bulletCount > 1) {
                const spreadAngle = (i - (weapon.bulletCount - 1) / 2) * 0.2;
                bulletDirection.applyAxisAngle(new THREE.Vector3(0, 1, 0), spreadAngle);
            }
            
            const bullet = this.createBullet(position, bulletDirection, weapon, owner);
            this.bullets.push(bullet);
            this.scene.add(bullet.mesh);
        }
        
        this.lastFireTime = currentTime;
        console.log(`发射 ${weapon.bulletCount} 发炮弹`);
        return true;
    }
    
    /**
     * 创建炮弹（使用对象池优化）
     */
    createBullet(position, direction, weapon, owner) {
        let bullet;
        
        // 尝试从对象池获取
        if (this.bulletPool.length > 0) {
            bullet = this.bulletPool.pop();
            bullet.reset(position, direction, weapon.speed, weapon.damage, owner);
        } else {
            bullet = new Bullet(position, direction, weapon.speed, weapon.damage, owner);
        }
        
        return bullet;
    }
    
    /**
     * 更新所有炮弹
     */
    update(deltaTime) {
        // 更新所有炮弹
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];
            bullet.update(deltaTime);
            
            // 移除无效炮弹
            if (!bullet.isActive) {
                this.removeBullet(i);
            }
        }
    }
    
    /**
     * 移除炮弹
     */
    removeBullet(index) {
        const bullet = this.bullets[index];
        
        // 从场景中移除
        bullet.dispose(this.scene);
        
        // 回收到对象池
        if (this.bulletPool.length < 50) { // 限制对象池大小
            this.bulletPool.push(bullet);
        }
        
        // 从数组中移除
        this.bullets.splice(index, 1);
    }
    
    /**
     * 检查炮弹与目标的碰撞
     */
    checkCollisions(targets) {
        const hits = [];
        
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];
            
            for (const target of targets) {
                // 检查炮弹所有者，避免自己打自己
                if ((bullet.owner === 'player' && target.type === 'player') ||
                    (bullet.owner === 'enemy' && target.type === 'enemy')) {
                    continue;
                }
                
                if (bullet.checkCollision(target)) {
                    const damage = bullet.hit(target);
                    hits.push({
                        bullet: bullet,
                        target: target,
                        damage: damage
                    });
                    
                    this.removeBullet(i);
                    break; // 炮弹已命中，跳出目标循环
                }
            }
        }
        
        return hits;
    }
    
    /**
     * 切换武器
     */
    switchWeapon(weaponType) {
        if (this.weapons[weaponType]) {
            this.currentWeapon = weaponType;
            console.log(`切换到武器: ${weaponType}`);
            return true;
        }
        return false;
    }
    
    /**
     * 获取当前武器信息
     */
    getCurrentWeaponInfo() {
        return {
            type: this.currentWeapon,
            ...this.weapons[this.currentWeapon]
        };
    }
    
    /**
     * 清理所有炮弹
     */
    clearAllBullets() {
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            this.removeBullet(i);
        }
    }
    
    /**
     * 销毁武器系统
     */
    dispose() {
        this.clearAllBullets();
        this.bulletPool = [];
        console.log('武器系统已销毁');
    }
}
