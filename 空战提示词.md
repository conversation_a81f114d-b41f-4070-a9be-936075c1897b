# Three.js空战游戏开发提示词（优化版）

## 项目概述
请开发一款基于Three.js的3D空战游戏，融合贪吃蛇机制，创造独特的游戏体验。

## 核心功能需求

### 1. 基础空战系统
- **玩家飞机控制**
  - 键盘控制飞机在3D空间中飞行（WASD/方向键）
  - 平滑的飞行动画和物理效果
  - 飞机倾斜、俯仰等真实飞行姿态

- **射击系统**
  - 鼠标点击或空格键发射炮弹
  - 炮弹轨迹追踪和碰撞检测
  - 射击特效和音效（如可能）

- **敌机AI**
  - 多个敌机自动巡逻和攻击
  - 被击中后的爆炸效果
  - 敌机重生机制

### 2. 贪吃蛇融合机制
- **成长系统**
  - 击败敌机后，玩家飞机后面跟随"机尾节点"
  - 每击败一架敌机增加一个节点
  - 机尾节点跟随主机飞行轨迹

- **约束机制**
  - 飞机不能撞到自己的机尾节点
  - 撞到机尾则游戏结束
  - 机尾越长，操控难度越大但火力越强

### 3. 游戏进阶特性
- **多样化武器**
  - 根据机尾长度解锁不同武器
  - 侧翼机尾节点也可发射炮弹
  - 武器冷却时间和弹药管理
  
- **关卡系统**
  - 逐渐增加敌机数量和难度
  - 不同的3D环境场景（太空、云层、城市上空）
  - Boss战机制

- **视觉效果**
  - 粒子系统（爆炸、尾焰、推进器火焰等）
  - 动态光照和阴影
  - 流畅的60fps渲染
  - 天空盒和环境氛围

- **游戏机制增强**
  - 血量系统（撞击机尾或被敌机击中扣血）
  - 分数和排行榜系统
  - 道具收集（护盾、火力增强、加速等）
  - 存档和设置功能

## 技术要求

### Three.js技术栈
- 使用最新的Three.js版本
- WebGL渲染优化
- 高效的几何体和材质管理
- 碰撞检测系统
- 使用OrbitControls或自定义相机控制

### 代码架构与模块化

#### 模块化设计要求
- **游戏引擎模块** (GameEngine.js)
  - 主游戏循环和渲染管理
  - 场景初始化和资源加载
  - 全局游戏状态管理

- **玩家控制模块** (Player.js)
  - 玩家飞机类定义
  - 输入处理和飞行控制
  - 机尾节点管理系统

- **敌机AI模块** (Enemy.js)
  - 敌机行为逻辑
  - AI巡逻和攻击模式
  - 敌机生成和销毁管理

- **武器系统模块** (WeaponSystem.js)
  - 炮弹发射和轨迹
  - 碰撞检测算法
  - 不同武器类型管理

- **物理引擎模块** (Physics.js)
  - 3D碰撞检测
  - 飞行物理模拟
  - 爆炸和粒子效果

- **UI界面模块** (UI.js)
  - HUD显示（血量、分数、机尾长度）
  - 菜单系统
  - 游戏状态提示

- **音效管理模块** (AudioManager.js)
  - 背景音乐控制
  - 音效播放管理
  - 音量和设置控制

#### 代码规范要求
- **中文注释**：所有代码必须使用中文注释
  - 每个函数都要有详细的中文说明
  - 重要算法步骤要有行内注释
  - 变量命名可以英文，但要有中文注释说明用途

- **代码示例格式**：
```javascript
/**
 * 玩家飞机类 - 管理玩家飞机的所有行为
 * 包括移动、射击、机尾管理等功能
 */
class PlayerAircraft {
    constructor() {
        this.position = new THREE.Vector3(); // 飞机当前位置
        this.velocity = new THREE.Vector3(); // 飞机速度向量
        this.tailNodes = []; // 机尾节点数组
    }
    
    /**
     * 更新飞机位置和状态
     * @param {number} deltaTime - 时间间隔
     */
    update(deltaTime) {
        // 更新飞机位置逻辑
    }
}
```

### 性能优化要求
- **对象池管理**
  - 炮弹对象复用，避免频繁创建销毁
  - 敌机实例池化管理
  - 粒子效果优化

- **渲染优化**
  - LOD（细节层次）系统
  - 视锥体剔除
  - 批量渲染相同几何体

- **内存管理**
  - 及时释放不用的资源
  - 纹理和几何体复用
  - 避免内存泄漏

### 用户体验
- 响应式控制，低延迟操作
- 直观的UI界面
- 渐进式难度曲线
- 流畅的60fps性能目标
- 移动端适配考虑

## 创新亮点
1. **3D贪吃蛇机制** - 传统2D贪吃蛇在3D空战中的创新应用
2. **动态难度** - 机尾越长，火力越强但操控越困难
3. **立体空战** - 充分利用3D空间的战斗策略

## 预期成果
一个完整可玩的HTML文件，包含：
- 完整的游戏逻辑和模块化代码结构
- 流畅的3D渲染和优化性能
- 独特的游戏机制融合
- 良好的用户体验和界面设计
- 详细的中文代码注释便于维护扩展

## 开发优先级与增量测试

### 开发方法论 - 增量开发与持续测试
- **边开发边测试**：每完成一个功能模块立即进行测试验证
- **增量式构建**：确保每个阶段的代码都是可运行和可测试的
- **错误早发现**：通过频繁测试减少后期调试成本和复杂度
- **功能隔离测试**：每个模块独立测试，确保接口正确再集成

### 第一阶段（核心功能）
1. **基础3D场景搭建**
   - 开发完成后测试：场景渲染是否正常，帧率是否稳定
   - 验证点：Three.js初始化、相机控制、基础光照

2. **玩家飞机控制和移动**
   - 开发完成后测试：键盘输入响应、飞机移动流畅度
   - 验证点：控制延迟、边界检测、飞行物理效果

3. **简单的射击系统**
   - 开发完成后测试：炮弹发射、轨迹追踪、性能影响
   - 验证点：射击频率、炮弹回收、内存使用

4. **基础敌机AI**
   - 开发完成后测试：敌机移动逻辑、碰撞检测准确性
   - 验证点：AI行为合理性、多敌机性能、碰撞精度

### 第二阶段（贪吃蛇机制）
1. **机尾节点系统**
   - 开发完成后测试：节点跟随效果、视觉表现
   - 验证点：节点间距、跟随延迟、渲染性能

2. **碰撞检测（飞机与机尾）**
   - 开发完成后测试：自碰撞检测精度、游戏结束逻辑
   - 验证点：检测准确性、误判率、响应时间

3. **击败敌机后机尾增长**
   - 开发完成后测试：增长动画、系统整合稳定性
   - 验证点：增长逻辑、动画流畅度、长机尾性能

### 第三阶段（游戏完善）
1. **UI界面和HUD**
   - 开发完成后测试：界面响应、信息准确性
   - 验证点：UI布局、数据同步、交互反馈

2. **粒子效果和视觉优化**
   - 开发完成后测试：特效性能、视觉效果
   - 验证点：帧率影响、内存占用、效果质量

3. **音效和音乐**
   - 开发完成后测试：音频同步、音量控制
   - 验证点：音频延迟、资源加载、用户体验

4. **关卡和难度系统**
   - 开发完成后测试：难度曲线、游戏平衡性
   - 验证点：关卡过渡、难度合理性、长时间游戏稳定性

### 测试策略细化
- **单元测试**：每个函数和类的独立测试
- **集成测试**：模块间接口和数据传递测试
- **性能测试**：每次新增功能后的性能基准测试
- **用户体验测试**：操作流畅度和游戏手感测试
- **兼容性测试**：不同浏览器和设备的测试
- **压力测试**：长时间运行和极限情况测试

### 调试和优化指导
- **代码审查**：每个阶段完成后进行代码质量检查
- **性能分析**：使用浏览器开发工具监控performance
- **错误日志**：完善的错误捕获和日志记录系统
- **版本控制**：每个测试通过的版本进行标记保存

## 测试要求与质量保证
- **增量测试原则**：遵循"开发一点，测试一点"的原则，确保每个功能模块都经过充分验证
- **自动化测试**：在可能的情况下编写自动化测试用例
- **性能监控**：持续监控帧率、内存使用和加载时间
- **错误处理**：完善的异常捕获和用户友好的错误提示
- **跨平台兼容**：在主流浏览器中测试兼容性（Chrome、Firefox、Safari、Edge）
- **移动端适配**：考虑触屏设备的操作体验
- **代码质量**：每个阶段都要进行代码审查和重构优化

## 风险控制与问题预防
- **版本备份**：每个测试通过的版本都要保存备份
- **回滚机制**：如果新功能导致问题，能快速回滚到稳定版本
- **问题追踪**：建立问题记录和解决方案文档
- **性能基准**：设定性能指标，超出阈值时及时优化

请基于以上需求开发游戏，注重代码质量、游戏性和视觉效果的平衡。所有代码都要有详细的中文注释，采用模块化设计便于后续维护和功能扩展。