<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D空战游戏测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-size: 14px;
            z-index: 100;
        }
        
        #controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            font-size: 12px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="info">
        <div>3D空战游戏测试</div>
        <div>状态: <span id="status">加载中...</span></div>
    </div>
    
    <div id="controls">
        <div>WASD: 移动飞机</div>
        <div>空格/鼠标: 射击</div>
    </div>

    <!-- Three.js库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    
    <script>
        // 简化的测试代码
        let scene, camera, renderer, player, clock;
        let gameRunning = false;
        
        function init() {
            try {
                console.log('开始初始化测试游戏...');
                
                // 创建场景
                scene = new THREE.Scene();
                scene.fog = new THREE.Fog(0x000011, 50, 200);
                
                // 创建相机
                camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                camera.position.set(0, 5, 10);
                
                // 创建渲染器
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setClearColor(0x000011, 1.0);
                document.body.appendChild(renderer.domElement);
                
                // 创建时钟
                clock = new THREE.Clock();
                
                // 添加光照
                const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
                scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
                directionalLight.position.set(10, 10, 5);
                scene.add(directionalLight);
                
                // 创建星空背景
                createStarField();
                
                // 创建玩家飞机
                createPlayer();
                
                // 绑定事件
                bindEvents();
                
                // 开始游戏循环
                gameRunning = true;
                document.getElementById('status').textContent = '运行中';
                animate();
                
                console.log('测试游戏初始化完成');
                
            } catch (error) {
                console.error('初始化失败:', error);
                document.getElementById('status').textContent = '初始化失败: ' + error.message;
            }
        }
        
        function createStarField() {
            const starGeometry = new THREE.BufferGeometry();
            const starCount = 1000;
            const positions = new Float32Array(starCount * 3);
            
            for (let i = 0; i < starCount * 3; i += 3) {
                positions[i] = (Math.random() - 0.5) * 400;
                positions[i + 1] = (Math.random() - 0.5) * 400;
                positions[i + 2] = (Math.random() - 0.5) * 400;
            }
            
            starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            
            const starMaterial = new THREE.PointsMaterial({
                color: 0xffffff,
                size: 2,
                sizeAttenuation: false
            });
            
            const stars = new THREE.Points(starGeometry, starMaterial);
            scene.add(stars);
        }
        
        function createPlayer() {
            // 创建简单的玩家飞机
            const geometry = new THREE.BoxGeometry(2, 0.5, 1);
            const material = new THREE.MeshPhongMaterial({ color: 0x4169E1 });
            player = new THREE.Mesh(geometry, material);
            player.position.set(0, 0, 0);
            scene.add(player);
            
            // 添加机翼
            const wingGeometry = new THREE.BoxGeometry(4, 0.1, 0.8);
            const wingMaterial = new THREE.MeshPhongMaterial({ color: 0x2E8B57 });
            const wings = new THREE.Mesh(wingGeometry, wingMaterial);
            player.add(wings);
        }
        
        function bindEvents() {
            // 键盘控制
            const keys = {};
            
            document.addEventListener('keydown', (event) => {
                keys[event.code] = true;
            });
            
            document.addEventListener('keyup', (event) => {
                keys[event.code] = false;
            });
            
            // 更新玩家位置
            function updatePlayer() {
                if (!player) return;
                
                const speed = 10;
                const deltaTime = clock.getDelta();
                
                if (keys['KeyW'] || keys['ArrowUp']) {
                    player.position.z -= speed * deltaTime;
                }
                if (keys['KeyS'] || keys['ArrowDown']) {
                    player.position.z += speed * deltaTime;
                }
                if (keys['KeyA'] || keys['ArrowLeft']) {
                    player.position.x -= speed * deltaTime;
                    player.rotation.z = 0.3;
                } else if (keys['KeyD'] || keys['ArrowRight']) {
                    player.position.x += speed * deltaTime;
                    player.rotation.z = -0.3;
                } else {
                    player.rotation.z = 0;
                }
                if (keys['KeyQ']) {
                    player.position.y += speed * deltaTime;
                }
                if (keys['KeyE']) {
                    player.position.y -= speed * deltaTime;
                }
                
                // 边界检查
                const boundary = 30;
                player.position.x = Math.max(-boundary, Math.min(boundary, player.position.x));
                player.position.y = Math.max(-boundary, Math.min(boundary, player.position.y));
                player.position.z = Math.max(-boundary, Math.min(boundary, player.position.z));
                
                // 相机跟随
                const cameraOffset = new THREE.Vector3(-10, 5, 0);
                const targetPosition = player.position.clone().add(cameraOffset);
                camera.position.lerp(targetPosition, 0.05);
                camera.lookAt(player.position);
            }
            
            // 将更新函数暴露给动画循环
            window.updatePlayer = updatePlayer;
        }
        
        function animate() {
            if (!gameRunning) return;
            
            requestAnimationFrame(animate);
            
            // 更新玩家
            if (window.updatePlayer) {
                window.updatePlayer();
            }
            
            // 渲染场景
            renderer.render(scene, camera);
        }
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
