<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D空战贪吃蛇游戏 - 调试版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: 'Arial', sans-serif;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        #debug {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-size: 12px;
            z-index: 100;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 20px;
            z-index: 300;
            text-align: center;
        }
        
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="loading">
            <div class="loading-spinner"></div>
            <div>正在加载游戏...</div>
        </div>
        
        <div id="debug">
            <div><strong>调试信息:</strong></div>
            <div id="debugLog"></div>
        </div>
    </div>

    <!-- Three.js库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    
    <script type="module">
        // 调试日志函数
        function debugLog(message) {
            console.log(message);
            const debugElement = document.getElementById('debugLog');
            if (debugElement) {
                debugElement.innerHTML += '<div>' + message + '</div>';
                debugElement.scrollTop = debugElement.scrollHeight;
            }
        }
        
        // 错误处理
        window.addEventListener('error', (event) => {
            debugLog('错误: ' + event.error.message);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            debugLog('Promise错误: ' + event.reason);
        });
        
        try {
            debugLog('开始加载模块...');
            
            // 动态导入模块
            const { GameEngine } = await import('./js/GameEngine.js');
            debugLog('GameEngine模块加载成功');
            
            const { PlayerAircraft } = await import('./js/Player.js');
            debugLog('Player模块加载成功');
            
            const { WeaponSystem } = await import('./js/WeaponSystem.js');
            debugLog('WeaponSystem模块加载成功');
            
            const { EnemyManager } = await import('./js/Enemy.js');
            debugLog('Enemy模块加载成功');
            
            debugLog('所有模块加载完成，开始初始化游戏...');
            
            // 游戏主类
            class Game {
                constructor() {
                    this.engine = null;
                    this.isInitialized = false;
                }
                
                async init() {
                    try {
                        debugLog('创建游戏引擎...');
                        this.engine = new GameEngine();
                        
                        debugLog('初始化游戏引擎...');
                        const success = await this.engine.init();
                        if (!success) {
                            throw new Error('游戏引擎初始化失败');
                        }
                        debugLog('游戏引擎初始化成功');
                        
                        debugLog('创建武器系统...');
                        this.engine.weaponSystem = new WeaponSystem(this.engine.scene);
                        
                        debugLog('创建玩家飞机...');
                        this.engine.player = new PlayerAircraft(this.engine.scene, this.engine.weaponSystem);
                        
                        debugLog('创建敌机管理器...');
                        this.engine.enemyManager = new EnemyManager(this.engine.scene, this.engine.weaponSystem);
                        
                        debugLog('设置相机跟随...');
                        this.setupCameraFollow();
                        
                        this.isInitialized = true;
                        debugLog('游戏初始化完成');
                        return true;
                        
                    } catch (error) {
                        debugLog('游戏初始化失败: ' + error.message);
                        debugLog('错误堆栈: ' + error.stack);
                        return false;
                    }
                }
                
                setupCameraFollow() {
                    const player = this.engine.player;
                    const camera = this.engine.camera;
                    const cameraOffset = new THREE.Vector3(-10, 5, 0);
                    
                    const originalUpdateGameLogic = this.engine.updateGameLogic.bind(this.engine);
                    
                    this.engine.updateGameLogic = function() {
                        originalUpdateGameLogic();
                        
                        if (player && player.isActive) {
                            const targetPosition = player.position.clone().add(cameraOffset);
                            camera.position.lerp(targetPosition, 0.05);
                            camera.lookAt(player.position);
                        }
                    };
                }
                
                start() {
                    if (!this.isInitialized) {
                        debugLog('游戏未初始化，无法启动');
                        return;
                    }
                    
                    debugLog('启动游戏...');
                    this.engine.start();
                    
                    // 隐藏加载界面
                    const loading = document.getElementById('loading');
                    if (loading) {
                        loading.style.display = 'none';
                    }
                    
                    debugLog('游戏启动成功');
                }
            }
            
            // 初始化游戏
            debugLog('创建游戏实例...');
            const game = new Game();
            
            const success = await game.init();
            if (success) {
                game.start();
            } else {
                debugLog('游戏启动失败');
                document.getElementById('loading').innerHTML = `
                    <div style="color: red;">
                        <h3>游戏启动失败</h3>
                        <p>请查看调试信息</p>
                        <button onclick="location.reload()">重新加载</button>
                    </div>
                `;
            }
            
        } catch (error) {
            debugLog('模块加载失败: ' + error.message);
            debugLog('错误堆栈: ' + error.stack);
            
            document.getElementById('loading').innerHTML = `
                <div style="color: red;">
                    <h3>模块加载失败</h3>
                    <p>${error.message}</p>
                    <button onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    </script>
</body>
</html>
