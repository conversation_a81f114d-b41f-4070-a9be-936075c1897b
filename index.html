<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D空战贪吃蛇游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: 'Arial', sans-serif;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        /* HUD界面样式 */
        #hud {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        #hud .stat {
            margin-bottom: 10px;
        }
        
        /* 游戏状态提示 */
        #gameStatus {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
            z-index: 200;
            display: none;
        }
        
        /* 控制说明 */
        #controls {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: white;
            font-size: 14px;
            text-align: right;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }
        
        /* 加载界面 */
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 20px;
            z-index: 300;
        }
        
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <!-- 游戏画布 -->
        <canvas id="gameCanvas"></canvas>
        
        <!-- 加载界面 -->
        <div id="loading">
            <div class="loading-spinner"></div>
            <div>正在加载游戏...</div>
        </div>
        
        <!-- HUD界面 -->
        <div id="hud">
            <div class="stat">血量: <span id="health">100</span></div>
            <div class="stat">分数: <span id="score">0</span></div>
            <div class="stat">机尾长度: <span id="tailLength">0</span></div>
            <div class="stat">敌机数量: <span id="enemyCount">0</span></div>
        </div>
        
        <!-- 游戏状态提示 */
        <div id="gameStatus">
            <div id="statusText">游戏暂停</div>
            <div style="font-size: 16px; margin-top: 10px;">按空格键继续</div>
        </div>
        
        <!-- 控制说明 */
        <div id="controls">
            <div>WASD / 方向键: 控制飞机</div>
            <div>鼠标点击 / 空格键: 射击</div>
            <div>P: 暂停游戏</div>
            <div>R: 重新开始</div>
        </div>
    </div>

    <!-- Three.js库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    
    <!-- 游戏模块 -->
    <script type="module" src="js/GameEngine.js"></script>
    <script type="module" src="js/Player.js"></script>
    <script type="module" src="js/Enemy.js"></script>
    <script type="module" src="js/WeaponSystem.js"></script>
    <script type="module" src="js/Physics.js"></script>
    <script type="module" src="js/UI.js"></script>
    <script type="module" src="js/AudioManager.js"></script>
    
    <!-- 主游戏入口 -->
    <script type="module" src="js/main.js"></script>
</body>
</html>
