<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D空战贪吃蛇游戏 - 完整版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: 'Arial', sans-serif;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        /* HUD界面样式 */
        #hud {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 10px;
        }
        
        #hud .stat {
            margin-bottom: 8px;
        }
        
        /* 游戏状态提示 */
        #gameStatus {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
            z-index: 200;
            display: none;
            background: rgba(0,0,0,0.8);
            padding: 30px;
            border-radius: 15px;
        }
        
        /* 控制说明 */
        #controls {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: white;
            font-size: 14px;
            text-align: right;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 10px;
        }
        
        /* 加载界面 */
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 20px;
            z-index: 300;
            text-align: center;
        }
        
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 游戏说明 */
        #instructions {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 14px;
            z-index: 100;
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 10px;
            max-width: 250px;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <!-- 游戏画布 -->
        <canvas id="gameCanvas"></canvas>
        
        <!-- 加载界面 -->
        <div id="loading">
            <div class="loading-spinner"></div>
            <div>正在加载3D空战贪吃蛇游戏...</div>
        </div>
        
        <!-- HUD界面 -->
        <div id="hud">
            <div class="stat">🛩️ 血量: <span id="health">100</span></div>
            <div class="stat">🎯 分数: <span id="score">0</span></div>
            <div class="stat">🐍 机尾长度: <span id="tailLength">0</span></div>
            <div class="stat">👾 敌机数量: <span id="enemyCount">0</span></div>
        </div>
        
        <!-- 游戏说明 -->
        <div id="instructions">
            <h4>🎮 游戏说明</h4>
            <p>这是一款融合了贪吃蛇机制的3D空战游戏！</p>
            <ul>
                <li>击败敌机会增加机尾长度</li>
                <li>机尾越长，火力越强</li>
                <li>小心不要撞到自己的机尾！</li>
                <li>撞到机尾会受到大量伤害</li>
            </ul>
        </div>
        
        <!-- 游戏状态提示 -->
        <div id="gameStatus">
            <div id="statusText">游戏暂停</div>
            <div style="font-size: 16px; margin-top: 10px;">按空格键继续</div>
        </div>
        
        <!-- 控制说明 -->
        <div id="controls">
            <div><strong>🎮 控制方式</strong></div>
            <div>WASD / 方向键: 控制飞机</div>
            <div>Q/E: 上升/下降</div>
            <div>鼠标点击 / 空格键: 射击</div>
            <div>P: 暂停游戏</div>
            <div>R: 重新开始</div>
        </div>
    </div>

    <!-- Three.js库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    
    <script type="module">
        // 导入所有模块
        import { GameEngine } from './js/GameEngine.js';
        import { PlayerAircraft } from './js/Player.js';
        import { WeaponSystem } from './js/WeaponSystem.js';
        import { EnemyManager } from './js/Enemy.js';

        /**
         * 游戏主类 - 协调各个模块
         */
        class Game {
            constructor() {
                this.engine = null;
                this.isInitialized = false;
                this.score = 0;
            }
            
            /**
             * 初始化游戏
             */
            async init() {
                try {
                    console.log('🚀 开始初始化3D空战贪吃蛇游戏...');
                    
                    // 创建游戏引擎
                    this.engine = new GameEngine();
                    
                    // 初始化引擎
                    const success = await this.engine.init();
                    if (!success) {
                        throw new Error('游戏引擎初始化失败');
                    }
                    
                    // 创建武器系统
                    this.createWeaponSystem();
                    
                    // 创建玩家飞机
                    this.createPlayer();
                    
                    // 创建敌机管理器
                    this.createEnemyManager();
                    
                    // 重写敌机摧毁处理，添加分数系统
                    this.setupScoreSystem();
                    
                    this.isInitialized = true;
                    console.log('✅ 游戏初始化完成');
                    
                    return true;
                    
                } catch (error) {
                    console.error('❌ 游戏初始化失败:', error);
                    this.showError('游戏初始化失败: ' + error.message);
                    return false;
                }
            }
            
            /**
             * 创建武器系统
             */
            createWeaponSystem() {
                this.engine.weaponSystem = new WeaponSystem(this.engine.scene);
                console.log('🔫 武器系统创建完成');
            }
            
            /**
             * 创建敌机管理器
             */
            createEnemyManager() {
                this.engine.enemyManager = new EnemyManager(this.engine.scene, this.engine.weaponSystem);
                console.log('👾 敌机管理器创建完成');
            }
            
            /**
             * 创建玩家飞机
             */
            createPlayer() {
                // 创建玩家飞机实例
                this.engine.player = new PlayerAircraft(this.engine.scene, this.engine.weaponSystem);
                
                // 设置相机跟随玩家
                this.setupCameraFollow();
                
                console.log('🛩️ 玩家飞机创建完成');
            }
            
            /**
             * 设置分数系统
             */
            setupScoreSystem() {
                const originalOnEnemyDestroyed = this.engine.onEnemyDestroyed.bind(this.engine);
                const game = this;
                
                this.engine.onEnemyDestroyed = function(enemy) {
                    // 调用原始处理
                    originalOnEnemyDestroyed(enemy);
                    
                    // 增加分数
                    game.score += 100;
                    console.log(`🎯 得分! 当前分数: ${game.score}`);
                };
            }
            
            /**
             * 设置相机跟随玩家
             */
            setupCameraFollow() {
                const player = this.engine.player;
                const camera = this.engine.camera;
                
                // 相机跟随偏移量
                const cameraOffset = new THREE.Vector3(-10, 5, 0);
                
                // 在游戏引擎的更新循环中添加相机跟随逻辑
                const originalUpdateGameLogic = this.engine.updateGameLogic.bind(this.engine);
                const game = this; // 保存游戏实例引用
                
                this.engine.updateGameLogic = function() {
                    // 调用原始更新逻辑
                    originalUpdateGameLogic();
                    
                    // 更新相机位置跟随玩家
                    if (player && player.isActive) {
                        const targetPosition = player.position.clone().add(cameraOffset);
                        camera.position.lerp(targetPosition, 0.05);
                        camera.lookAt(player.position);
                    }
                    
                    // 更新UI显示
                    game.updateGameUI();
                };
            }
            
            /**
             * 启动游戏
             */
            start() {
                if (!this.isInitialized) {
                    console.error('❌ 游戏未初始化，无法启动');
                    return;
                }
                
                // 启动游戏引擎
                this.engine.start();
                
                // 更新UI显示
                this.updateUI();
                
                // 隐藏说明
                setTimeout(() => {
                    const instructions = document.getElementById('instructions');
                    if (instructions) {
                        instructions.style.opacity = '0.7';
                    }
                }, 5000);
                
                console.log('🎮 游戏启动成功');
            }
            
            /**
             * 更新UI显示（初始化时调用）
             */
            updateUI() {
                // 更新HUD信息
                const healthElement = document.getElementById('health');
                const scoreElement = document.getElementById('score');
                const tailLengthElement = document.getElementById('tailLength');
                const enemyCountElement = document.getElementById('enemyCount');
                
                if (healthElement) healthElement.textContent = '100';
                if (scoreElement) scoreElement.textContent = '0';
                if (tailLengthElement) tailLengthElement.textContent = '0';
                if (enemyCountElement) enemyCountElement.textContent = '0';
            }
            
            /**
             * 更新游戏UI显示（每帧调用）
             */
            updateGameUI() {
                if (!this.engine.player || !this.engine.enemyManager) return;
                
                // 更新血量
                const healthElement = document.getElementById('health');
                if (healthElement) {
                    healthElement.textContent = this.engine.player.health.toString();
                }
                
                // 更新分数
                const scoreElement = document.getElementById('score');
                if (scoreElement) {
                    scoreElement.textContent = this.score.toString();
                }
                
                // 更新机尾长度
                const tailLengthElement = document.getElementById('tailLength');
                if (tailLengthElement) {
                    tailLengthElement.textContent = this.engine.player.tailLength.toString();
                }
                
                // 更新敌机数量
                const enemyCountElement = document.getElementById('enemyCount');
                if (enemyCountElement) {
                    const enemyCount = this.engine.enemyManager.getEnemies().length;
                    enemyCountElement.textContent = enemyCount.toString();
                }
            }
            
            /**
             * 显示错误信息
             */
            showError(message) {
                const loading = document.getElementById('loading');
                if (loading) {
                    loading.innerHTML = `
                        <div style="color: red; text-align: center;">
                            <h3>❌ 错误</h3>
                            <p>${message}</p>
                            <button onclick="location.reload()" style="
                                padding: 10px 20px;
                                background: #007bff;
                                color: white;
                                border: none;
                                border-radius: 5px;
                                cursor: pointer;
                            ">🔄 重新加载</button>
                        </div>
                    `;
                }
            }
        }

        /**
         * 页面加载完成后初始化游戏
         */
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('📄 页面加载完成，开始初始化游戏');
            
            // 检查浏览器兼容性
            if (!window.WebGLRenderingContext) {
                console.error('❌ 浏览器不支持WebGL');
                document.getElementById('loading').innerHTML = `
                    <div style="color: red; text-align: center;">
                        <h3>❌ 浏览器不兼容</h3>
                        <p>您的浏览器不支持WebGL，无法运行此游戏</p>
                        <p>请使用现代浏览器（Chrome、Firefox、Safari、Edge）</p>
                    </div>
                `;
                return;
            }
            
            // 创建游戏实例
            const game = new Game();
            
            // 初始化游戏
            const success = await game.init();
            if (success) {
                // 启动游戏
                game.start();
            }
        });

        /**
         * 页面卸载时清理资源
         */
        window.addEventListener('beforeunload', () => {
            console.log('🧹 页面即将卸载，清理游戏资源');
            // 这里可以添加资源清理逻辑
        });
    </script>
</body>
</html>
