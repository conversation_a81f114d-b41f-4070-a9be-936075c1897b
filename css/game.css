/**
 * 3D空战贪吃蛇游戏样式表
 * 包含所有游戏界面的样式定义
 */

/* 基础样式 */
body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    background: #000;
    font-family: 'Arial', sans-serif;
}

#gameContainer {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#gameCanvas {
    display: block;
    width: 100%;
    height: 100%;
}

/* HUD界面样式 */
#hud {
    position: absolute;
    top: 20px;
    left: 20px;
    color: white;
    font-size: 18px;
    z-index: 100;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    background: rgba(0,0,0,0.3);
    padding: 15px;
    border-radius: 10px;
}

#hud .stat {
    margin-bottom: 8px;
}

/* 游戏状态提示 */
#gameStatus {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 24px;
    text-align: center;
    z-index: 200;
    display: none;
    background: rgba(0,0,0,0.8);
    padding: 30px;
    border-radius: 15px;
}

/* 控制说明 */
#controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: white;
    font-size: 14px;
    text-align: right;
    z-index: 100;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    background: rgba(0,0,0,0.3);
    padding: 15px;
    border-radius: 10px;
}

/* 加载界面 */
#loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 20px;
    z-index: 300;
    text-align: center;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 游戏说明 */
#instructions {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 14px;
    z-index: 100;
    background: rgba(0,0,0,0.3);
    padding: 15px;
    border-radius: 10px;
    max-width: 250px;
}

#instructions h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #3498db;
}

#instructions ul {
    margin: 10px 0;
    padding-left: 20px;
}

#instructions li {
    margin-bottom: 5px;
    line-height: 1.4;
}

/* 错误信息样式 */
.error-container {
    color: red;
    text-align: center;
}

.error-container h3 {
    margin-bottom: 15px;
}

.error-container p {
    margin: 10px 0;
    line-height: 1.5;
}

.error-button {
    padding: 10px 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 15px;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.error-button:hover {
    background: #0056b3;
}

.error-button:active {
    background: #004085;
}

/* 游戏状态文本样式 */
.status-subtitle {
    font-size: 16px;
    margin-top: 10px;
    opacity: 0.8;
}

/* 说明文本样式 */
.instructions-fade {
    transition: opacity 0.5s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #hud {
        font-size: 16px;
        padding: 10px;
    }
    
    #controls {
        font-size: 12px;
        padding: 10px;
    }
    
    #instructions {
        font-size: 12px;
        padding: 10px;
        max-width: 200px;
    }
    
    #gameStatus {
        font-size: 20px;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    #instructions {
        display: none; /* 在小屏幕上隐藏说明 */
    }
    
    #hud {
        font-size: 14px;
        padding: 8px;
    }
    
    #controls {
        font-size: 11px;
        padding: 8px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    #hud, #controls, #instructions {
        background: rgba(0,0,0,0.9);
        border: 1px solid white;
    }
    
    .error-button {
        border: 2px solid white;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .loading-spinner {
        animation: none;
    }
    
    .instructions-fade {
        transition: none;
    }
    
    .error-button {
        transition: none;
    }
}
