# 3D空战贪吃蛇游戏

一款融合了贪吃蛇机制的创新3D空战游戏，使用Three.js开发。

## 🎮 游戏特色

### 核心创新
- **3D贪吃蛇机制**: 传统2D贪吃蛇在3D空战中的创新应用
- **动态难度**: 机尾越长，火力越强但操控越困难
- **立体空战**: 充分利用3D空间的战斗策略

### 游戏机制
- 击败敌机会增加机尾长度
- 机尾节点跟随玩家飞机移动
- 撞到自己的机尾会受到大量伤害
- 敌机具有智能AI，会追击和攻击玩家

## 🚀 已实现功能

### ✅ 第一阶段：基础3D场景搭建
- **渲染系统**: WebGL渲染器，支持阴影和光照
- **场景环境**: 星空背景、雾效、地面参考平面
- **光照系统**: 环境光、方向光、补充光源
- **相机系统**: 透视相机，支持跟随玩家

### ✅ 玩家飞机控制和移动
- **3D飞机模型**: 包含机身、机翼、尾翼、驾驶舱
- **流畅控制**: WASD/方向键控制，Q/E上升下降
- **飞行物理**: 加速度、最大速度限制、阻力系统
- **飞行姿态**: 倾斜、俯仰等真实飞行效果
- **推进器特效**: 动态火焰效果
- **边界检测**: 防止飞机飞出游戏区域

### ✅ 射击系统
- **炮弹发射**: 鼠标点击或空格键射击
- **轨迹追踪**: 炮弹飞行轨迹和生命周期
- **碰撞检测**: 精确的3D碰撞检测
- **对象池优化**: 炮弹对象复用，提高性能
- **多种武器**: 基础、快速、散射武器类型
- **射击冷却**: 防止无限射击的冷却机制

### ✅ 基础敌机AI
- **智能行为**: 巡逻、追击、攻击、撤退状态机
- **3D敌机模型**: 红色敌机，区别于玩家
- **AI寻路**: 预测性射击，智能移动
- **动态生成**: 自动生成敌机，维持游戏挑战
- **血量系统**: 敌机可被击败

### ✅ 第二阶段：贪吃蛇机制
- **机尾节点系统**: 动态生成和管理机尾节点
- **跟随效果**: 机尾节点平滑跟随玩家轨迹
- **视觉表现**: 发光效果、透明度变化
- **自碰撞检测**: 检测玩家是否撞到自己的机尾
- **机尾增长**: 击败敌机后自动增加机尾长度

## 🎯 游戏控制

### 飞机控制
- **WASD** 或 **方向键**: 控制飞机移动
- **Q**: 上升
- **E**: 下降
- **鼠标点击** 或 **空格键**: 射击

### 游戏控制
- **P**: 暂停/恢复游戏
- **R**: 重新开始游戏

## 📊 游戏界面

### HUD显示
- 🛩️ 血量: 显示当前生命值
- 🎯 分数: 击败敌机获得分数
- 🐍 机尾长度: 当前机尾节点数量
- 👾 敌机数量: 场景中的敌机数量

## 🏗️ 技术架构

### 模块化设计
- **GameEngine.js**: 主游戏循环和渲染管理
- **Player.js**: 玩家飞机控制和机尾系统
- **Enemy.js**: 敌机AI和管理器
- **WeaponSystem.js**: 武器和炮弹系统
- **Physics.js**: 物理引擎（预留）
- **UI.js**: 界面管理（预留）
- **AudioManager.js**: 音效管理（预留）

### 性能优化
- **对象池**: 炮弹和敌机对象复用
- **LOD系统**: 距离相关的细节层次
- **视锥体剔除**: 只渲染可见对象
- **帧率控制**: 稳定60fps性能

## 🎮 游戏文件

### 可运行版本
- **final.html**: 完整功能版本（推荐）
- **game.html**: 标准版本
- **debug.html**: 调试版本
- **test.html**: 简化测试版本

### 开发文件
- **index.html**: 原始版本
- **js/**: 所有JavaScript模块

## 🚀 运行游戏

1. 启动本地服务器:
   ```bash
   python3 -m http.server 8000
   ```

2. 在浏览器中打开:
   ```
   http://localhost:8000/final.html
   ```

3. 使用现代浏览器（Chrome、Firefox、Safari、Edge）

## 🔮 后续开发计划

### 第三阶段：游戏完善
- [ ] 粒子效果系统（爆炸、尾焰、推进器）
- [ ] 音效和背景音乐
- [ ] 关卡系统和难度调整
- [ ] 更丰富的UI界面
- [ ] 道具系统（护盾、火力增强等）
- [ ] 多种敌机类型
- [ ] Boss战机制

### 高级功能
- [ ] 多人对战模式
- [ ] 排行榜系统
- [ ] 成就系统
- [ ] 自定义飞机外观
- [ ] 地图编辑器

## 🎯 游戏亮点

1. **创新玩法**: 首次将贪吃蛇机制应用到3D空战游戏
2. **技术实现**: 纯JavaScript + Three.js，无需额外插件
3. **性能优化**: 多种优化技术确保流畅运行
4. **模块化架构**: 易于维护和扩展
5. **中文注释**: 所有代码都有详细的中文注释

## 📝 开发日志

- ✅ 基础3D场景搭建完成
- ✅ 玩家飞机控制系统完成
- ✅ 射击系统和碰撞检测完成
- ✅ 敌机AI系统完成
- ✅ 贪吃蛇机制完成
- ✅ 机尾节点系统完成
- ✅ 自碰撞检测完成
- ✅ 基础游戏循环完成

游戏已具备完整的核心玩法，可以正常游玩！🎉
